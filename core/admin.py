from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django import forms
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import User, Organization, Role, Permission, RolePermission, Department

# Custom forms for User admin
class CustomUserCreationForm(UserCreationForm):
    """Custom user creation form for admin"""
    class Meta(UserCreationForm.Meta):
        model = User
        fields = ('email', 'username', 'organization', 'role')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['organization'].required = True
        self.fields['role'].required = True

class CustomUserChangeForm(UserChangeForm):
    """Custom user change form for admin"""
    class Meta(UserChangeForm.Meta):
        model = User
        fields = '__all__'

# Inline classes
class RolePermissionInline(admin.TabularInline):
    """Inline for managing role permissions"""
    model = RolePermission
    extra = 0
    autocomplete_fields = ['permission']

class RoleInline(admin.TabularInline):
    """Inline for managing organization roles"""
    model = Role
    extra = 0
    fields = ['role_name', 'description']
    readonly_fields = ['created_at']

class DepartmentInline(admin.TabularInline):
    """Inline for managing organization departments"""
    model = Department
    extra = 0
    fields = ['department_name', 'description']
    readonly_fields = ['created_at']

# Admin classes
@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Admin interface for Organization model"""
    list_display = ['company_name', 'industry_type', 'total_users', 'total_roles', 'total_departments', 'created_at']
    list_filter = ['industry_type', 'created_at']
    search_fields = ['company_name', 'industry_type']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [RoleInline, DepartmentInline]
    
    fieldsets = (
        ('Organization Information', {
            'fields': ('company_name', 'industry_type')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_users(self, obj):
        """Count total users in organization"""
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:core_user_changelist') + f'?organization__id__exact={obj.id}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return count
    total_users.short_description = 'Total Users'
    
    def total_roles(self, obj):
        """Count total roles in organization"""
        count = obj.role_set.count()
        return count
    total_roles.short_description = 'Roles'
    
    def total_departments(self, obj):
        """Count total departments in organization"""
        count = obj.department_set.count()
        return count
    total_departments.short_description = 'Departments'

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """Admin interface for Role model"""
    list_display = ['role_name', 'organization', 'total_users', 'total_permissions', 'created_at']
    list_filter = ['organization', 'created_at']
    search_fields = ['role_name', 'description', 'organization__company_name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [RolePermissionInline]
    autocomplete_fields = ['organization']
    
    fieldsets = (
        ('Role Information', {
            'fields': ('organization', 'role_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_users(self, obj):
        """Count users with this role"""
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:core_user_changelist') + f'?role__id__exact={obj.id}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return count
    total_users.short_description = 'Users'
    
    def total_permissions(self, obj):
        """Count permissions for this role"""
        count = obj.rolepermission_set.count()
        return count
    total_permissions.short_description = 'Permissions'

@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """Admin interface for Permission model"""
    list_display = ['permission_name', 'description', 'total_roles', 'created_at']
    list_filter = ['created_at']
    search_fields = ['permission_name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Permission Information', {
            'fields': ('permission_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_roles(self, obj):
        """Count roles with this permission"""
        count = obj.rolepermission_set.count()
        return count
    total_roles.short_description = 'Roles'

@admin.register(RolePermission)
class RolePermissionAdmin(admin.ModelAdmin):
    """Admin interface for RolePermission model"""
    list_display = ['role', 'permission', 'role_organization', 'created_at'
]
    list_filter = ['role__organization', 'created_at']
    search_fields = ['role__role_name', 'permission__permission_name', 'role__organization__company_name']
    autocomplete_fields = ['role', 'permission']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Role Permission Mapping', {
            'fields': ('role', 'permission')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def role_organization(self, obj):
        """Show organization of the role"""
        return obj.role.organization.company_name
    role_organization.short_description = 'Organization'

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Admin interface for Department model"""
    list_display = ['department_name', 'organization', 'total_employees', 'created_at']
    list_filter = ['organization', 'created_at']
    search_fields = ['department_name', 'description', 'organization__company_name']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['organization']
    
    fieldsets = (
        ('Department Information', {
            'fields': ('organization', 'department_name', 'description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_employees(self, obj):
        """Count employees in department"""
        # This assumes you have a Profile model with department_ref field
        try:
            from profiles.models import Profile
            count = Profile.objects.filter(department_ref=obj).count()
            return count
        except ImportError:
            return 'N/A'
    total_employees.short_description = 'Employees'

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom admin interface for User model"""
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm
    
    list_display = [
        'username', 'email', 'get_full_name', 'organization', 'role', 
        'access_level', 'is_active', 'is_staff', 'last_login', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'access_level', 
        'organization', 'role', 'created_at'
    ]
    search_fields = ['username', 'email', 'organization__company_name', 'role__role_name']
    readonly_fields = ['last_login', 'created_at', 'updated_at']
    autocomplete_fields = ['organization', 'role']
    filter_horizontal = ()  # <-- Add this line to override BaseUserAdmin
    
    fieldsets = (
        ('Authentication', {
            'fields': ('username', 'email', 'password')
        }),
        ('Organization & Role', {
            'fields': ('organization', 'role', 'access_level')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser'),
        }),
        ('Important Dates', {
            'fields': ('last_login', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        ('Create New User', {
            'classes': ('wide',),
            'fields': ('username', 'email', 'organization', 'role', 'password1', 'password2')
        }),
        ('Additional Information', {
            'classes': ('wide',),
            'fields': ('access_level', 'is_active', 'is_staff')
        }),
    )
    
    ordering = ['-created_at']
    
    def get_full_name(self, obj):
        """Display full name from profile if available"""
        try:
            if hasattr(obj, 'profile') and obj.profile:
                full_name = obj.get_full_name()
                if full_name != obj.username:
                    return format_html('<span title="Profile: {}">{}</span>', obj.profile.emp_id, full_name)
            return obj.username
        except:
            return obj.username
    get_full_name.short_description = 'Full Name'
    
    def get_queryset(self, request):
        """Optimize queries with select_related"""
        return super().get_queryset(request).select_related(
            'organization', 'role'
        ).prefetch_related('role__rolepermission_set')
    
    def save_model(self, request, obj, form, change):
        """Custom save logic for user model"""
        if not change:  # Creating new user
            # Ensure password is properly hashed
            if hasattr(form, 'cleaned_data') and 'password1' in form.cleaned_data:
                obj.set_password(form.cleaned_data['password1'])
        super().save_model(request, obj, form, change)

# Custom admin site configuration
admin.site.site_header = 'Mhcognition HR Management System'
admin.site.site_title = 'MHCognition HRMS Admin'
admin.site.index_title = 'Welcome to MHCognition HR Management System Administration'

