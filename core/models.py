from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from django.conf import settings
from django.utils import timezone
from core.fields import EncryptedField

# -----------------------------
# Custom User Manager
# -----------------------------
class CustomUserManager(BaseUserManager):
    def create_user(self, email, username, password=None, **extra_fields):
        if not email:
            raise ValueError("Email is required")
        if 'organization' not in extra_fields or not extra_fields['organization']:
            raise ValueError("Organization is required")
        if 'role' not in extra_fields or not extra_fields['role']:
            raise ValueError("Role is required")
        
        user = self.model(
            email=self.normalize_email(email),
            username=username,
            **extra_fields
        )
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, username, password=None, **extra_fields):
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_staff", True)

        # Create a default organization for superuser if none exists
        if 'organization' not in extra_fields:
            organization, created = Organization.objects.get_or_create(
                company_name="Default Organization",
                defaults={'industry_type': 'Technology'}
            )
            extra_fields['organization'] = organization

            # Create an Admin role if it doesn't exist
            admin_role, created = Role.objects.get_or_create(
                organization=organization,
                role_name="Admin",
                defaults={'description': 'System Administrator'}
            )
            extra_fields['role'] = admin_role

        return self.create_user(email, username, password, **extra_fields)

# -----------------------------
# Organization
# -----------------------------
class Organization(models.Model):
    company_name = models.CharField(max_length=150, unique=True)
    industry_type = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name

# -----------------------------
# Role
# -----------------------------
class Role(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    role_name = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.role_name

# -----------------------------
# Permission
# -----------------------------
class Permission(models.Model):
    permission_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# -----------------------------
# RolePermission Mapping
# -----------------------------
class RolePermission(models.Model):
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('role', 'permission')

    def __str__(self):
        return f"{self.role.role_name} - {self.permission.permission_name}"

# -----------------------------
# Department
# -----------------------------
class Department(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    department_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# -----------------------------
# Custom User Model
# -----------------------------
class User(AbstractBaseUser):
    ACCESS_LEVELS = [
        ('Emp1', 'Basic'),
        ('Emp2', 'Team Member'),
        ('Emp3', 'Team Lead'),
        ('Emp4', 'Department Head'),
        ('Hr1', 'HR Director'),
        ('Hr2', 'HR Manager'),
        ('Hr3', 'HR Executive'),
    ]

    access_level = models.CharField(max_length=20, choices=ACCESS_LEVELS, default='Emp1')
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.CASCADE, null=False)
    
    # REMOVED: profile field - now Profile references User instead
    # profile = models.OneToOneField('profiles.Profile', on_delete=models.SET_NULL, null=True, blank=True, db_column='emp_id')
    
    email = models.EmailField(unique=True)
    username = models.CharField(max_length=50, unique=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)  # for admin panel
    is_superuser = models.BooleanField(default=False)  # for admin panel
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    objects = CustomUserManager()

    def __str__(self):
        return self.username

    def has_permission(self, perm_name):
        if self.role:
            return self.role.rolepermission_set.filter(permission__permission_name=perm_name).exists()
        return False

    def has_perm(self, perm, obj=None):
        """
        Return True if the user has the specified permission. Query all
        available auth backends, but return immediately if any backend
        returns True. Thus, a user who has permission from a single auth
        backend is assumed to have permission in general.
        """
        # Superusers have all permissions
        if self.is_superuser:
            return True

        # Check custom permissions through role
        return self.has_permission(perm)

    def has_module_perms(self, app_label):
        """
        Return True if the user has any permissions in the given app label.
        Use similar logic as has_perm(), above.
        """
        # Superusers have all permissions
        if self.is_superuser:
            return True

        # For now, return True if user is staff (can be customized)
        return self.is_staff

    def get_full_name(self):
        """Return the full name for the user."""
        # Access profile via reverse relationship: self.profile
        if hasattr(self, 'profile') and self.profile:
            return f"{self.profile.first_name} {self.profile.last_name}".strip()
        return self.username

    def get_short_name(self):
        """Return the short name for the user."""
        # Access profile via reverse relationship: self.profile
        if hasattr(self, 'profile') and self.profile and self.profile.first_name:
            return self.profile.first_name
        return self.username
