from functools import wraps
from django.http import JsonResponse

def role_required(*roles):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            user = request.user
            if user.is_superuser or (user.role and user.role.role_name in roles):
                return view_func(self, request, *args, **kwargs)
            return JsonResponse({"error": "Access denied. Insufficient role."}, status=403)
        return wrapper
    return decorator
