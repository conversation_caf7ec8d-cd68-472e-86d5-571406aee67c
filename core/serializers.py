from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import Organization, Role, User, Department
from profiles.models import Profile

class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    
    # Core user fields
    organization = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all())
    role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all())
    access_level = serializers.ChoiceField(choices=User.ACCESS_LEVELS)
    
    # Professional Info
    designation = serializers.CharField(max_length=100)
    date_of_joining = serializers.DateField()
    work_location = serializers.CharField(max_length=100)
    employment_type = serializers.CharField(max_length=50)
    department_ref = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(), 
        required=False, 
        allow_null=True
    )
    reporting_manager = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), 
        required=False, 
        allow_null=True
    )

    class Meta:
        model = User
        fields = [
            'email', 'username', 'password', 'confirm_password',
            'organization', 'role', 'access_level',
            'designation', 'date_of_joining', 'work_location', 'employment_type',
            'department_ref', 'reporting_manager'
        ]

    def validate(self, attrs):
        """Custom validation for the registration data"""
        # Password confirmation
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Password and confirm password do not match.")
        
        # Role-Organization validation
        role = attrs.get('role')
        organization = attrs.get('organization')
        if role and organization and role.organization != organization:
            raise serializers.ValidationError("Selected role must belong to the selected organization.")
        
        # Department-Organization validation
        department = attrs.get('department_ref')
        if department and organization and department.organization != organization:
            raise serializers.ValidationError("Selected department must belong to the selected organization.")
        
        # Reporting manager validation
        reporting_manager = attrs.get('reporting_manager')
        if reporting_manager and organization and reporting_manager.organization != organization:
            raise serializers.ValidationError("Reporting manager must belong to the same organization.")
        
        return attrs

    def create(self, validated_data):
        # Remove confirm_password as it's not needed for user creation
        validated_data.pop('confirm_password', None)
        
        # Extract profile data
        profile_data = {
            'organization': validated_data.pop('organization'),
            'designation': validated_data.pop('designation'),
            'date_of_joining': validated_data.pop('date_of_joining'),
            'work_location': validated_data.pop('work_location'),
            'employment_type': validated_data.pop('employment_type'),
            'department_ref': validated_data.pop('department_ref', None),
            'reporting_manager': validated_data.pop('reporting_manager', None),
            'email_id': validated_data['email'],  # Use email from user
        }
        
        # Create user first
        user = User.objects.create_user(**validated_data)
        
        # Create associated profile with minimal required data
        profile_data['user'] = user
        # Add placeholder values for required Profile fields
        profile_data.update({
            'first_name': validated_data.get('username', 'User'),  # Temporary placeholder
            'last_name': '',  # Can be updated later
            'gender': 'Not Specified',
            'date_of_birth': '1990-01-01',  # Placeholder
            'phone_number': '0000000000',  # Placeholder
            'emergency_contact_number': '0000000000',  # Placeholder
            'address': 'To be updated',  # Placeholder
            'city': 'Unknown',
            'state': 'Unknown',
            'pincode': '000000',
            'aadhar_number': '000000000000',  # Placeholder
            'pan_number': '**********',  # Placeholder
            'marital_status': 'Not Specified',
            'previous_company_name': '',
            'years_of_experience': 0,
            'highest_qualification': 'To be updated',
            'college_university_name': 'To be updated',
            'graduation_year': 2020,  # Placeholder
        })
        
        profile = Profile.objects.create(**profile_data)
        
        return user
class ProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = [
            'first_name', 'last_name', 'gender', 'date_of_birth',
            'phone_number', 'emergency_contact_number', 'address',
            'city', 'state', 'pincode', 'aadhar_number', 'pan_number',
            'marital_status', 'previous_company_name', 'years_of_experience',
            'highest_qualification', 'college_university_name', 'graduation_year'
        ]
        read_only_fields = ['emp_id', 'created_at', 'updated_at']

    def validate_aadhar_number(self, value):
        """Validate Aadhar number"""
        import re
        aadhar = re.sub(r'\D', '', value)
        if len(aadhar) != 12:
            raise serializers.ValidationError('Aadhar number must be 12 digits')
        return aadhar

    def validate_pan_number(self, value):
        """Validate PAN number"""
        import re
        pan = value.upper().strip()
        if not re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', pan):
            raise serializers.ValidationError('Invalid PAN format (e.g., **********)')
        return pan
