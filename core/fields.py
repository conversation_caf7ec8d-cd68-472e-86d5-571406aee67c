from django.db import models
from django.conf import settings
import base64

try:
    from cryptography.fernet import Fernet
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    print("Warning: cryptography not available, using Text<PERSON>ield instead of Encry<PERSON><PERSON>ield")

class Encrypted<PERSON>ield(models.TextField):
    """Custom field that encrypts data before storing in database."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if ENCRYPTION_AVAILABLE:
            # Generate encryption key from Django secret key
            key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].ljust(32, '0').encode())
            self.cipher = Fernet(key)
        else:
            self.cipher = None

    def from_db_value(self, value, expression, connection):
        """Decrypt value when reading from database."""
        if value is None:
            return value
        if not ENCRYPTION_AVAILABLE or not self.cipher:
            return value
        try:
            return self.cipher.decrypt(value.encode()).decode()
        except:
            # Return original value if decryption fails (for migration compatibility)
            return value

    def to_python(self, value):
        """Convert value to Python type."""
        if isinstance(value, str) or value is None:
            return value
        return str(value)

    def get_prep_value(self, value):
        """Encrypt value before storing in database."""
        if value is None:
            return value
        if not ENCRYPTION_AVAILABLE or not self.cipher:
            return value
        try:
            return self.cipher.encrypt(str(value).encode()).decode()
        except:
            return value
