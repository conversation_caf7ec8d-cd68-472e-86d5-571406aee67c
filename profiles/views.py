from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from .models import Profile , BankDetails
from .serializers import ProfileSerializer , BankDetailsSerializer
from core.models import User
from core.decorators import role_required  # You should have this
from rest_framework.permissions import IsAuthenticated


class ProfileViewSet(viewsets.ModelViewSet):
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        # Prevent errors during Swagger schema generation
        if getattr(self, 'swagger_fake_view', False):
            return Profile.objects.none()

        # Anonymous users: return nothing
        if not user.is_authenticated:
            return Profile.objects.none()

        # Admins and HR can see all profiles
        if user.is_superuser or (hasattr(user, 'role') and user.role and user.role.role_name in ['Admin', 'HR']):
            return Profile.objects.all()

        # Access Level 1: Own profile only
        if getattr(user, 'access_level', None) == 'Emp1' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(emp_id=user.profile.emp_id)

        # Access Level 2: Team members (reportees)
        elif getattr(user, 'access_level', None) == 'Emp2' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(reporting_manager=user.profile)

        # Access Level 3: Department-level view
        elif getattr(user, 'access_level', None) == 'Emp3' and hasattr(user, 'profile') and user.profile:
            return Profile.objects.filter(department_ref=user.profile.department_ref)

        return Profile.objects.none()

    @role_required('HR')
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @role_required('HR')
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @role_required('HR')
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @role_required('HR')
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['get'], url_path='me')
    def me(self, request):
        """Fetch the profile of the currently logged-in user."""
        if not hasattr(request.user, 'profile') or request.user.profile is None:
            return Response({"error": "No profile associated with user."}, status=404)

        serializer = self.get_serializer(request.user.profile)
        return Response(serializer.data)

class BankDetailsViewSet(viewsets.ModelViewSet):
    queryset = BankDetails.objects.all()
    serializer_class = BankDetailsSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if getattr(self, 'swagger_fake_view', False):
            return BankDetails.objects.none()

        if not user.is_authenticated:
            return BankDetails.objects.none()

        # HR and Admin can view all
        if user.is_superuser or (hasattr(user, 'role') and user.role and user.role.role_name in ['Admin', 'HR']):
            return BankDetails.objects.all()

        # Others can only view their own bank details
        if hasattr(user, 'profile') and user.profile:
            return BankDetails.objects.filter(profile=user.profile)

        return BankDetails.objects.none()

    # Only HR can create
    @role_required('HR')
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    # Only HR can update
    @role_required('HR')
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @role_required('HR')
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    # Only HR can delete
    @role_required('HR')
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)