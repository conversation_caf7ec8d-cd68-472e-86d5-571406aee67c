from rest_framework import serializers
from .models import Profile , BankDetails

class ProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = '__all__'  # or list fields explicitly for more control
        read_only_fields = ['emp_id', 'created_at', 'updated_at']


class BankDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankDetails
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
